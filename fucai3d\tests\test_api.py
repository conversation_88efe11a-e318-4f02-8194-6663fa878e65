# P10-Web界面系统 API测试
# 基础的API端点测试用例

import pytest
import asyncio
from fastapi.testclient import TestClient
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.web.app import app

# 创建测试客户端
client = TestClient(app)

class TestHealthEndpoints:
    """健康检查端点测试"""
    
    def test_health_check(self):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "components" in data
        assert "uptime" in data
    
    def test_readiness_check(self):
        """测试就绪检查端点"""
        response = client.get("/ready")
        assert response.status_code == 200
        
        data = response.json()
        assert "ready" in data
        assert "timestamp" in data
        assert "checks" in data

class TestPredictionAPI:
    """预测API测试"""
    
    def test_get_latest_predictions(self):
        """测试获取最新预测"""
        response = client.get("/api/prediction/latest?limit=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert isinstance(data["data"], list)
    
    def test_get_prediction_statistics(self):
        """测试获取预测统计"""
        response = client.get("/api/prediction/statistics")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
    
    def test_get_accuracy_trends(self):
        """测试获取准确率趋势"""
        response = client.get("/api/prediction/accuracy-trends?days=7")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert isinstance(data["data"], list)
    
    def test_get_performance_comparison(self):
        """测试获取性能对比"""
        response = client.get("/api/prediction/performance-comparison?period=week&metric=accuracy")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data

class TestMonitoringAPI:
    """监控API测试"""
    
    def test_get_system_status(self):
        """测试获取系统状态"""
        response = client.get("/api/monitoring/status")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "system_health" in data["data"]
        assert "database_status" in data["data"]
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        response = client.get("/api/monitoring/metrics?hours=24")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
    
    def test_get_optimization_tasks(self):
        """测试获取优化任务"""
        response = client.get("/api/monitoring/tasks")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data

class TestOptimizationAPI:
    """优化API测试"""
    
    def test_get_optimization_config(self):
        """测试获取优化配置"""
        response = client.get("/api/optimization/config")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "model_settings" in data["data"]
        assert "optimization_settings" in data["data"]
    
    def test_system_diagnostics(self):
        """测试系统诊断"""
        response = client.get("/api/optimization/diagnostics")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "system_info" in data["data"]
        assert "database_check" in data["data"]
    
    def test_trigger_optimization(self):
        """测试触发优化任务"""
        payload = {
            "task_type": "parameter_tuning",
            "parameters": {"epochs": 50, "learning_rate": 0.01}
        }
        
        response = client.post("/api/optimization/trigger", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "task_id" in data["data"]

class TestCacheAPI:
    """缓存API测试"""
    
    def test_get_cache_stats(self):
        """测试获取缓存统计"""
        response = client.get("/api/cache/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "size" in data["data"]
        assert "hit_rate" in data["data"]
    
    def test_cache_performance(self):
        """测试缓存性能指标"""
        response = client.get("/api/cache/performance")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "hit_rate" in data["data"]
        assert "recommendations" in data["data"]
    
    def test_cache_health(self):
        """测试缓存健康检查"""
        response = client.get("/api/cache/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "health_status" in data["data"]

class TestErrorHandling:
    """错误处理测试"""
    
    def test_invalid_endpoint(self):
        """测试无效端点"""
        response = client.get("/api/invalid/endpoint")
        assert response.status_code == 404
    
    def test_invalid_parameters(self):
        """测试无效参数"""
        response = client.get("/api/prediction/latest?limit=invalid")
        assert response.status_code == 422  # Validation error
    
    def test_invalid_task_type(self):
        """测试无效任务类型"""
        payload = {"task_type": "invalid_type"}
        response = client.post("/api/optimization/trigger", json=payload)
        assert response.status_code == 400

class TestPerformance:
    """性能测试"""
    
    def test_api_response_time(self):
        """测试API响应时间"""
        import time
        
        start_time = time.time()
        response = client.get("/api/prediction/latest?limit=10")
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        assert response.status_code == 200
        assert response_time < 1000  # 响应时间应小于1秒
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        import concurrent.futures
        import time
        
        def make_request():
            return client.get("/health")
        
        start_time = time.time()
        
        # 并发发送10个请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            responses = [future.result() for future in futures]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 所有请求都应该成功
        for response in responses:
            assert response.status_code == 200
        
        # 总时间应该合理（并发处理）
        assert total_time < 5  # 10个请求在5秒内完成

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
