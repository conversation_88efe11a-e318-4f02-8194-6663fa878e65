import React, { useState, useEffect } from 'react'
import { Card, Select, Spin, Alert } from 'antd'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts'
import { usePredictionData } from '../hooks/usePredictionData'

const { Option } = Select

interface ChartData {
  digit: number
  probability: number
  count: number
}

const ProbabilityChart: React.FC = () => {
  const [position, setPosition] = useState<'hundreds' | 'tens' | 'units'>('hundreds')
  const [chartData, setChartData] = useState<ChartData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { fetchProbabilityDistribution } = usePredictionData()

  useEffect(() => {
    loadProbabilityData()
  }, [position])

  const loadProbabilityData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const data = await fetchProbabilityDistribution(position)
      
      // 转换数据格式
      const formattedData = data.map(item => ({
        digit: item[position] as number,
        probability: (item.avg_probability * 100), // 转换为百分比
        count: item.count
      })).sort((a, b) => a.digit - b.digit)

      setChartData(formattedData)
    } catch (err) {
      setError('获取概率分布数据失败')
      console.error('获取概率分布失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const getPositionName = (pos: string) => {
    switch (pos) {
      case 'hundreds': return '百位'
      case 'tens': return '十位'
      case 'units': return '个位'
      default: return '未知'
    }
  }

  const getBarColor = (probability: number) => {
    if (probability >= 12) return '#ff4d4f' // 高概率 - 红色
    if (probability >= 10) return '#faad14' // 中等概率 - 橙色
    return '#1890ff' // 低概率 - 蓝色
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div style={{
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>
            数字: {label}
          </p>
          <p style={{ margin: '4px 0 0 0', color: '#1890ff' }}>
            概率: {data.probability.toFixed(2)}%
          </p>
          <p style={{ margin: '4px 0 0 0', color: '#666' }}>
            出现次数: {data.count}
          </p>
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <Card title="📊 概率分布" size="small">
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin />
          <p style={{ marginTop: 16, color: '#666' }}>加载中...</p>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card title="📊 概率分布" size="small">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button 
              onClick={loadProbabilityData}
              style={{ 
                border: 'none', 
                background: 'none', 
                color: '#1890ff', 
                cursor: 'pointer' 
              }}
            >
              重试
            </button>
          }
        />
      </Card>
    )
  }

  return (
    <Card 
      title="📊 概率分布" 
      size="small"
      extra={
        <Select
          value={position}
          onChange={setPosition}
          size="small"
          style={{ width: 80 }}
        >
          <Option value="hundreds">百位</Option>
          <Option value="tens">十位</Option>
          <Option value="units">个位</Option>
        </Select>
      }
    >
      <div style={{ height: 200 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="digit" 
              tick={{ fontSize: 12 }}
              axisLine={{ stroke: '#d9d9d9' }}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              axisLine={{ stroke: '#d9d9d9' }}
              label={{ value: '概率(%)', angle: -90, position: 'insideLeft', style: { fontSize: 12 } }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="probability" radius={[2, 2, 0, 0]}>
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(entry.probability)} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div style={{ 
        marginTop: 12, 
        padding: '8px 12px', 
        background: '#fafafa', 
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
          <span style={{ fontWeight: 'bold' }}>
            {getPositionName(position)}概率分布
          </span>
          <span style={{ color: '#666' }}>
            总数据点: {chartData.length}
          </span>
        </div>
        <div style={{ display: 'flex', gap: 16, fontSize: '11px' }}>
          <span>
            <span style={{ color: '#ff4d4f' }}>●</span> 高概率 (≥12%)
          </span>
          <span>
            <span style={{ color: '#faad14' }}>●</span> 中概率 (10-12%)
          </span>
          <span>
            <span style={{ color: '#1890ff' }}>●</span> 低概率 (&lt;10%)
          </span>
        </div>
      </div>
    </Card>
  )
}

export default ProbabilityChart
