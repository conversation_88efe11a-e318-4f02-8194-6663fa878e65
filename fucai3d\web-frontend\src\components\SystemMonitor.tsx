import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Progress, Tag, Alert, Spin, Button, Tooltip } from 'antd'
import { 
  MonitorOutlined, 
  DatabaseOutlined, 
  RocketOutlined, 
  WarningOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  PlayCircleOutlined
} from '@ant-design/icons'
import axios from 'axios'

interface SystemStatus {
  system_health: string
  database_status: string
  optimization_status: {
    is_running: boolean
    last_run: string | null
    next_scheduled: string | null
    active_tasks: number
  }
  performance_summary: {
    avg_response_time: number
    success_rate: number
    error_rate: number
    throughput: number
  }
  active_components: string[]
  system_resources: {
    cpu_percent: number
    memory_percent: number
    disk_percent: number
    network_io: {
      bytes_sent: number
      bytes_recv: number
    }
  }
  last_update: string
}

const SystemMonitor: React.FC = () => {
  const [systemData, setSystemData] = useState<SystemStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchSystemStatus()
    // 设置定时刷新
    const interval = setInterval(fetchSystemStatus, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [])

  const fetchSystemStatus = async () => {
    try {
      setError(null)
      const response = await axios.get('/api/monitoring/status')
      
      if (response.data.status === 'success') {
        setSystemData(response.data.data)
      } else {
        setError('获取系统状态失败')
      }
    } catch (err) {
      setError('连接监控服务失败')
      console.error('获取系统状态失败:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchSystemStatus()
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return '#52c41a'
      case 'warning': return '#faad14'
      case 'critical': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getHealthText = (health: string) => {
    switch (health) {
      case 'healthy': return '健康'
      case 'warning': return '警告'
      case 'critical': return '严重'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'green'
      case 'disconnected': return 'red'
      case 'error': return 'red'
      default: return 'orange'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载系统监控数据...</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="监控数据加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={handleRefresh} icon={<ReloadOutlined />}>
            重试
          </Button>
        }
      />
    )
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>🖥️ P9系统监控</h2>
        <Button 
          onClick={handleRefresh} 
          loading={refreshing}
          icon={<ReloadOutlined />}
        >
          刷新
        </Button>
      </div>

      {/* 系统状态概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统健康"
              value={getHealthText(systemData?.system_health || 'unknown')}
              prefix={<MonitorOutlined />}
              valueStyle={{ color: getHealthColor(systemData?.system_health || 'unknown') }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="数据库状态"
              value={systemData?.database_status === 'connected' ? '已连接' : '未连接'}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: getStatusColor(systemData?.database_status || 'unknown') }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="优化任务"
              value={systemData?.optimization_status?.active_tasks || 0}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="成功率"
              value={((systemData?.performance_summary?.success_rate || 0) * 100).toFixed(1)}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* 系统资源使用情况 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={12}>
          <Card title="💻 系统资源" size="small">
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>CPU使用率</span>
                <span>{systemData?.system_resources?.cpu_percent?.toFixed(1) || 0}%</span>
              </div>
              <Progress 
                percent={systemData?.system_resources?.cpu_percent || 0} 
                status={systemData?.system_resources?.cpu_percent && systemData.system_resources.cpu_percent > 80 ? 'exception' : 'normal'}
              />
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>内存使用率</span>
                <span>{systemData?.system_resources?.memory_percent?.toFixed(1) || 0}%</span>
              </div>
              <Progress 
                percent={systemData?.system_resources?.memory_percent || 0}
                status={systemData?.system_resources?.memory_percent && systemData.system_resources.memory_percent > 80 ? 'exception' : 'normal'}
              />
            </div>

            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>磁盘使用率</span>
                <span>{systemData?.system_resources?.disk_percent?.toFixed(1) || 0}%</span>
              </div>
              <Progress 
                percent={systemData?.system_resources?.disk_percent || 0}
                status={systemData?.system_resources?.disk_percent && systemData.system_resources.disk_percent > 90 ? 'exception' : 'normal'}
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="📊 性能指标" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="平均响应时间"
                  value={systemData?.performance_summary?.avg_response_time?.toFixed(1) || 0}
                  suffix="ms"
                  valueStyle={{ fontSize: '16px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="吞吐量"
                  value={systemData?.performance_summary?.throughput || 0}
                  suffix="req/s"
                  valueStyle={{ fontSize: '16px' }}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>错误率</span>
                <span>{((systemData?.performance_summary?.error_rate || 0) * 100).toFixed(2)}%</span>
              </div>
              <Progress 
                percent={(systemData?.performance_summary?.error_rate || 0) * 100}
                status={(systemData?.performance_summary?.error_rate || 0) > 0.1 ? 'exception' : 'success'}
                size="small"
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 活跃组件和网络状态 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="🔧 活跃组件" size="small">
            <div style={{ minHeight: 120 }}>
              {systemData?.active_components?.length ? (
                systemData.active_components.map((component, index) => (
                  <Tag key={index} color="blue" style={{ marginBottom: 8 }}>
                    <CheckCircleOutlined style={{ marginRight: 4 }} />
                    {component}
                  </Tag>
                ))
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                  <WarningOutlined style={{ fontSize: '24px', marginBottom: 8 }} />
                  <div>暂无活跃组件</div>
                </div>
              )}
            </div>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="🌐 网络状态" size="small">
            <div style={{ minHeight: 120 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Tooltip title="发送字节数">
                    <Statistic
                      title="发送"
                      value={formatBytes(systemData?.system_resources?.network_io?.bytes_sent || 0)}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Tooltip>
                </Col>
                <Col span={12}>
                  <Tooltip title="接收字节数">
                    <Statistic
                      title="接收"
                      value={formatBytes(systemData?.system_resources?.network_io?.bytes_recv || 0)}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Tooltip>
                </Col>
              </Row>
              
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Tag color={systemData?.optimization_status?.is_running ? 'green' : 'orange'}>
                  {systemData?.optimization_status?.is_running ? (
                    <>
                      <PlayCircleOutlined /> 优化运行中
                    </>
                  ) : (
                    '优化已停止'
                  )}
                </Tag>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最后更新时间 */}
      <div style={{ textAlign: 'center', marginTop: 16, color: '#666', fontSize: '12px' }}>
        最后更新: {systemData?.last_update ? new Date(systemData.last_update).toLocaleString() : '--'}
      </div>
    </div>
  )
}

export default SystemMonitor
