import React, { useState, useEffect } from 'react'
import { Card, Table, Tag, Button, Progress, Modal, Form, Select, Input, message, Tooltip } from 'antd'
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ReloadOutlined,
  PlusOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import axios from 'axios'

const { Option } = Select
const { TextArea } = Input

interface OptimizationTask {
  task_id: string
  task_type: string
  task_status: string
  start_time: string
  end_time?: string
  progress: number
  result_summary?: string
  error_message?: string
}

const OptimizationTasks: React.FC = () => {
  const [tasks, setTasks] = useState<OptimizationTask[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [triggering, setTriggering] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchTasks()
    // 设置定时刷新
    const interval = setInterval(fetchTasks, 15000) // 15秒刷新一次
    return () => clearInterval(interval)
  }, [])

  const fetchTasks = async () => {
    try {
      const response = await axios.get('/api/monitoring/tasks')
      
      if (response.data.status === 'success') {
        setTasks(response.data.data)
      }
    } catch (err) {
      console.error('获取优化任务失败:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchTasks()
  }

  const handleTriggerOptimization = async (values: any) => {
    try {
      setTriggering(true)
      
      const response = await axios.post('/api/monitoring/trigger-optimization', {
        task_type: values.task_type,
        parameters: values.parameters ? JSON.parse(values.parameters) : {}
      })
      
      if (response.data.status === 'success') {
        message.success('优化任务已启动')
        setModalVisible(false)
        form.resetFields()
        await fetchTasks()
      } else {
        message.error(response.data.message || '启动优化任务失败')
      }
    } catch (err) {
      message.error('启动优化任务失败')
      console.error('启动优化任务失败:', err)
    } finally {
      setTriggering(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'blue'
      case 'completed': return 'green'
      case 'failed': return 'red'
      case 'pending': return 'orange'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'completed': return '已完成'
      case 'failed': return '失败'
      case 'pending': return '等待中'
      default: return '未知'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <PlayCircleOutlined />
      case 'completed': return <CheckCircleOutlined />
      case 'failed': return <ExclamationCircleOutlined />
      case 'pending': return <ClockCircleOutlined />
      default: return null
    }
  }

  const getTaskTypeText = (type: string) => {
    switch (type) {
      case 'parameter_optimization': return '参数优化'
      case 'model_training': return '模型训练'
      case 'data_analysis': return '数据分析'
      case 'performance_tuning': return '性能调优'
      default: return type
    }
  }

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = end.getTime() - start.getTime()
    
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    
    if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    }
    return `${seconds}秒`
  }

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 120,
      render: (id: string) => (
        <Tooltip title={id}>
          <span style={{ fontFamily: 'monospace' }}>
            {id.length > 10 ? `${id.substring(0, 10)}...` : id}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (type: string) => getTaskTypeText(type),
    },
    {
      title: '状态',
      dataIndex: 'task_status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: OptimizationTask) => (
        <div style={{ width: 100 }}>
          <Progress 
            percent={progress} 
            size="small" 
            status={record.task_status === 'failed' ? 'exception' : 'normal'}
          />
        </div>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '持续时间',
      key: 'duration',
      render: (record: OptimizationTask) => formatDuration(record.start_time, record.end_time),
    },
    {
      title: '结果',
      key: 'result',
      render: (record: OptimizationTask) => {
        if (record.task_status === 'failed' && record.error_message) {
          return (
            <Tooltip title={record.error_message}>
              <Tag color="red">失败</Tag>
            </Tooltip>
          )
        }
        if (record.task_status === 'completed' && record.result_summary) {
          return (
            <Tooltip title={record.result_summary}>
              <Tag color="green">成功</Tag>
            </Tooltip>
          )
        }
        return <span style={{ color: '#999' }}>--</span>
      },
    },
  ]

  return (
    <Card 
      title="🚀 优化任务管理"
      extra={
        <div style={{ display: 'flex', gap: 8 }}>
          <Button 
            onClick={handleRefresh} 
            loading={refreshing}
            icon={<ReloadOutlined />}
            size="small"
          >
            刷新
          </Button>
          <Button 
            type="primary"
            onClick={() => setModalVisible(true)}
            icon={<PlusOutlined />}
            size="small"
          >
            启动任务
          </Button>
        </div>
      }
    >
      <Table
        columns={columns}
        dataSource={tasks}
        rowKey="task_id"
        loading={loading}
        pagination={{ 
          pageSize: 10, 
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个任务`
        }}
        size="small"
        scroll={{ x: 800 }}
      />

      {/* 启动优化任务模态框 */}
      <Modal
        title="启动优化任务"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            loading={triggering}
            onClick={() => form.submit()}
          >
            启动任务
          </Button>
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleTriggerOptimization}
        >
          <Form.Item
            name="task_type"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="选择要执行的优化任务类型">
              <Option value="parameter_optimization">参数优化</Option>
              <Option value="model_training">模型训练</Option>
              <Option value="data_analysis">数据分析</Option>
              <Option value="performance_tuning">性能调优</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="parameters"
            label="任务参数"
            help="可选，JSON格式的任务参数"
          >
            <TextArea
              rows={4}
              placeholder='例如: {"epochs": 100, "learning_rate": 0.001}'
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 任务统计信息 */}
      <div style={{ 
        marginTop: 16, 
        padding: '12px', 
        background: '#fafafa', 
        borderRadius: '6px',
        display: 'flex',
        justifyContent: 'space-between',
        fontSize: '12px'
      }}>
        <div>
          <span style={{ color: '#1890ff' }}>●</span> 运行中: {tasks.filter(t => t.task_status === 'running').length}
          <span style={{ marginLeft: 16, color: '#52c41a' }}>●</span> 已完成: {tasks.filter(t => t.task_status === 'completed').length}
          <span style={{ marginLeft: 16, color: '#ff4d4f' }}>●</span> 失败: {tasks.filter(t => t.task_status === 'failed').length}
          <span style={{ marginLeft: 16, color: '#faad14' }}>●</span> 等待中: {tasks.filter(t => t.task_status === 'pending').length}
        </div>
        <div style={{ color: '#666' }}>
          总任务数: {tasks.length}
        </div>
      </div>
    </Card>
  )
}

export default OptimizationTasks
