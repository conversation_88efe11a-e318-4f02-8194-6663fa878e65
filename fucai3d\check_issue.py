import sqlite3
import os

def check_db(db_name):
    print(f'=== {db_name} ===')
    db_path = f'data/{db_name}'
    
    if not os.path.exists(db_path):
        print(f'{db_name} 不存在')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f'表数量: {len(tables)}')
    
    # 检查每个表的期号信息
    for table in tables:
        table_name = table[0]
        try:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'issue' in columns:
                cursor.execute(f"SELECT MIN(issue), MAX(issue), COUNT(*) FROM {table_name}")
                result = cursor.fetchone()
                if result[0]:
                    print(f'  {table_name}: {result[2]}条记录, 期号: {result[0]} - {result[1]}')
        except Exception as e:
            print(f'  {table_name}: 检查失败 - {e}')
    
    conn.close()
    print()

# 检查三个数据库
check_db('lottery.db')
check_db('fucai3d.db') 
check_db('alerts.db')
