import React, { useState, useEffect } from 'react'
import { Card, Tabs, Form, Input, InputNumber, Switch, Button, message, Spin, Alert, Divider } from 'antd'
import { SaveOutlined, ReloadOutlined, SettingOutlined, DatabaseOutlined, RobotOutlined, ToolOutlined } from '@ant-design/icons'
import axios from 'axios'

const { TabPane } = Tabs

interface OptimizationConfig {
  model_settings: {
    xgboost: any
    lightgbm: any
    lstm: any
  }
  optimization_settings: {
    auto_optimization: boolean
    optimization_interval: number
    performance_threshold: number
    max_concurrent_tasks: number
    enable_notifications: boolean
  }
  data_settings: {
    training_data_size: number
    validation_split: number
    feature_selection: boolean
    data_preprocessing: boolean
    outlier_detection: boolean
  }
  system_settings: {
    log_level: string
    max_log_files: number
    backup_frequency: number
    cleanup_old_models: boolean
    model_retention_days: number
  }
}

const SystemSettings: React.FC = () => {
  const [config, setConfig] = useState<OptimizationConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await axios.get('/api/optimization/config')
      
      if (response.data.status === 'success') {
        setConfig(response.data.data)
        form.setFieldsValue(response.data.data)
      } else {
        setError('获取配置失败')
      }
    } catch (err) {
      setError('连接服务器失败')
      console.error('获取配置失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const values = await form.validateFields()
      
      const response = await axios.put('/api/optimization/config', values)
      
      if (response.data.status === 'success') {
        message.success('配置已保存')
        setConfig(values)
      } else {
        message.error(response.data.message || '保存配置失败')
      }
    } catch (err) {
      message.error('保存配置失败')
      console.error('保存配置失败:', err)
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (config) {
      form.setFieldsValue(config)
      message.info('配置已重置')
    }
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载系统配置...</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="配置加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={fetchConfig} icon={<ReloadOutlined />}>
            重试
          </Button>
        }
      />
    )
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>⚙️ 系统设置</h2>
        <div style={{ display: 'flex', gap: 8 }}>
          <Button onClick={handleReset} icon={<ReloadOutlined />}>
            重置
          </Button>
          <Button 
            type="primary" 
            onClick={handleSave} 
            loading={saving}
            icon={<SaveOutlined />}
          >
            保存配置
          </Button>
        </div>
      </div>

      <Form form={form} layout="vertical">
        <Tabs defaultActiveKey="optimization" type="card">
          {/* 优化设置 */}
          <TabPane tab={<span><RobotOutlined />优化设置</span>} key="optimization">
            <Card title="自动优化配置" size="small" style={{ marginBottom: 16 }}>
              <Form.Item
                name={['optimization_settings', 'auto_optimization']}
                label="启用自动优化"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name={['optimization_settings', 'optimization_interval']}
                label="优化间隔 (小时)"
                rules={[{ required: true, message: '请输入优化间隔' }]}
              >
                <InputNumber min={1} max={168} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['optimization_settings', 'performance_threshold']}
                label="性能阈值"
                rules={[{ required: true, message: '请输入性能阈值' }]}
              >
                <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['optimization_settings', 'max_concurrent_tasks']}
                label="最大并发任务数"
                rules={[{ required: true, message: '请输入最大并发任务数' }]}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['optimization_settings', 'enable_notifications']}
                label="启用通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Card>
          </TabPane>

          {/* 模型设置 */}
          <TabPane tab={<span><DatabaseOutlined />模型设置</span>} key="models">
            <Card title="XGBoost 配置" size="small" style={{ marginBottom: 16 }}>
              <Form.Item
                name={['model_settings', 'xgboost', 'n_estimators']}
                label="估计器数量"
              >
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'xgboost', 'max_depth']}
                label="最大深度"
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'xgboost', 'learning_rate']}
                label="学习率"
              >
                <InputNumber min={0.001} max={1} step={0.001} style={{ width: '100%' }} />
              </Form.Item>
            </Card>

            <Card title="LightGBM 配置" size="small" style={{ marginBottom: 16 }}>
              <Form.Item
                name={['model_settings', 'lightgbm', 'n_estimators']}
                label="估计器数量"
              >
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'lightgbm', 'max_depth']}
                label="最大深度"
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'lightgbm', 'num_leaves']}
                label="叶子节点数"
              >
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Card>

            <Card title="LSTM 配置" size="small">
              <Form.Item
                name={['model_settings', 'lstm', 'units']}
                label="神经元数量"
              >
                <InputNumber min={10} max={200} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'lstm', 'dropout']}
                label="Dropout率"
              >
                <InputNumber min={0} max={0.9} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['model_settings', 'lstm', 'epochs']}
                label="训练轮数"
              >
                <InputNumber min={10} max={500} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </TabPane>

          {/* 数据设置 */}
          <TabPane tab={<span><ToolOutlined />数据设置</span>} key="data">
            <Card title="数据处理配置" size="small">
              <Form.Item
                name={['data_settings', 'training_data_size']}
                label="训练数据大小"
                rules={[{ required: true, message: '请输入训练数据大小' }]}
              >
                <InputNumber min={100} max={10000} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['data_settings', 'validation_split']}
                label="验证集比例"
                rules={[{ required: true, message: '请输入验证集比例' }]}
              >
                <InputNumber min={0.1} max={0.5} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Divider />
              
              <Form.Item
                name={['data_settings', 'feature_selection']}
                label="启用特征选择"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name={['data_settings', 'data_preprocessing']}
                label="启用数据预处理"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name={['data_settings', 'outlier_detection']}
                label="启用异常值检测"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Card>
          </TabPane>

          {/* 系统设置 */}
          <TabPane tab={<span><SettingOutlined />系统设置</span>} key="system">
            <Card title="系统配置" size="small">
              <Form.Item
                name={['system_settings', 'log_level']}
                label="日志级别"
              >
                <Input placeholder="INFO" />
              </Form.Item>
              
              <Form.Item
                name={['system_settings', 'max_log_files']}
                label="最大日志文件数"
              >
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name={['system_settings', 'backup_frequency']}
                label="备份频率 (天)"
              >
                <InputNumber min={1} max={30} style={{ width: '100%' }} />
              </Form.Item>
              
              <Divider />
              
              <Form.Item
                name={['system_settings', 'cleanup_old_models']}
                label="自动清理旧模型"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name={['system_settings', 'model_retention_days']}
                label="模型保留天数"
              >
                <InputNumber min={1} max={365} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </TabPane>
        </Tabs>
      </Form>
    </div>
  )
}

export default SystemSettings
