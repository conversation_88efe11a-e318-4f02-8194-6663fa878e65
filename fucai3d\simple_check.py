import sqlite3
import os

print("Database Issue Check")
print("=" * 40)

# Check fucai3d.db
db_path = "data/fucai3d.db"
if os.path.exists(db_path):
    print("\nChecking fucai3d.db:")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check final_predictions table
    try:
        cursor.execute("SELECT COUNT(*) FROM final_predictions")
        count = cursor.fetchone()[0]
        print(f"  final_predictions: {count} records")
        
        if count > 0:
            cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 5")
            issues = cursor.fetchall()
            latest_issues = [issue[0] for issue in issues]
            print(f"  Latest issues: {latest_issues}")
            
            # Check if 2025209 exists
            cursor.execute("SELECT COUNT(*) FROM final_predictions WHERE issue = '2025209'")
            count_2025209 = cursor.fetchone()[0]
            print(f"  Records with issue 2025209: {count_2025209}")
            
    except Exception as e:
        print(f"  final_predictions: error - {e}")
    
    conn.close()
else:
    print("fucai3d.db not found")

# Check lottery.db
db_path = "data/lottery.db"
if os.path.exists(db_path):
    print("\nChecking lottery.db:")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"  Found {len(tables)} tables")
    
    for table in tables:
        table_name = table[0]
        try:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            
            if "issue" in columns:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                if count > 0:
                    cursor.execute(f"SELECT MAX(issue) FROM {table_name}")
                    max_issue = cursor.fetchone()[0]
                    print(f"  {table_name}: {count} records, max issue: {max_issue}")
                    
                    # Check if 2025209 exists
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE issue = '2025209'")
                    count_2025209 = cursor.fetchone()[0]
                    if count_2025209 > 0:
                        print(f"    -> Has 2025209: {count_2025209} records")
        except Exception as e:
            print(f"  {table_name}: error - {e}")
    
    conn.close()
else:
    print("lottery.db not found")

print("\nCheck complete")
