import sqlite3
import os

db_path = 'data/fucai3d.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='final_predictions'")
    result = cursor.fetchone()
    
    if result:
        print('final_predictions table exists')
        cursor.execute('SELECT COUNT(*) FROM final_predictions')
        count = cursor.fetchone()[0]
        print(f'Records: {count}')
        
        if count > 0:
            cursor.execute('SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 3')
            issues = cursor.fetchall()
            print(f'Latest issues: {[issue[0] for issue in issues]}')
        else:
            print('Table is empty')
    else:
        print('final_predictions table does not exist')
    
    conn.close()
else:
    print('Database not found')
