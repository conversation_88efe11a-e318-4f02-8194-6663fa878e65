#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查福彩3D项目中的数据库文件
"""

import sqlite3
import os

def check_database(db_file):
    """检查单个数据库文件"""
    db_path = os.path.join('data', db_file)
    if not os.path.exists(db_path):
        print(f'{db_file} 不存在')
        return
    
    print(f'\n=== {db_file} ===')
    print(f'文件大小: {os.path.getsize(db_path) / 1024:.2f} KB')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f'表数量: {len(tables)}')
        
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]
                print(f'  - {table_name}: {count} 条记录')
                
                # 检查是否有期号字段
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                if 'issue' in column_names:
                    cursor.execute(f'SELECT MIN(issue), MAX(issue) FROM {table_name} WHERE issue IS NOT NULL')
                    result = cursor.fetchone()
                    if result[0] and result[1]:
                        print(f'    期号范围: {result[0]} - {result[1]}')
                
                # 检查最近的记录
                if 'created_at' in column_names or 'timestamp' in column_names:
                    time_col = 'created_at' if 'created_at' in column_names else 'timestamp'
                    cursor.execute(f'SELECT MAX({time_col}) FROM {table_name}')
                    latest = cursor.fetchone()[0]
                    if latest:
                        print(f'    最新记录: {latest}')
                        
            except Exception as e:
                print(f'  - {table_name}: 检查失败 - {e}')
        
        conn.close()
        
    except Exception as e:
        print(f'打开数据库失败: {e}')

def main():
    """主函数"""
    print("福彩3D项目数据库检查")
    print("=" * 50)
    
    # 检查三个数据库文件
    db_files = ['alerts.db', 'fucai3d.db', 'lottery.db']
    
    for db_file in db_files:
        check_database(db_file)
    
    print("\n" + "=" * 50)
    print("检查完成")

if __name__ == '__main__':
    main()
