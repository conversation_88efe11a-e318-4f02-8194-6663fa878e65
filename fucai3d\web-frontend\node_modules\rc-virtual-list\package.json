{"name": "rc-virtual-list", "version": "3.19.1", "description": "React Virtual List Component", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "virtual-list"], "homepage": "https://github.com/react-component/virtual-list", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/virtual-list.git"}, "bugs": {"url": "https://github.com/react-component/virtual-list/issues"}, "files": ["lib", "es", "assets"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "test": "rc-test", "now-build": "npm run build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@types/classnames": "^2.2.10", "@types/enzyme": "^3.10.5", "@types/jest": "^25.1.3", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@types/warning": "^3.0.0", "cheerio": "1.0.0-rc.12", "cross-env": "^5.2.0", "dumi": "^2.2.17", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.15.6", "enzyme-to-json": "^3.1.4", "eslint": "^8.56.0", "eslint-plugin-unicorn": "^55.0.0", "father": "^4.4.0", "glob": "^7.1.6", "np": "^7.5.0", "rc-animate": "^2.9.1", "rc-test": "^7.0.15", "react": "16.14.0", "react-dom": "16.14.0", "typescript": "^5.0.0"}, "dependencies": {"@babel/runtime": "^7.20.0", "classnames": "^2.2.6", "rc-resize-observer": "^1.0.0", "rc-util": "^5.36.0"}}