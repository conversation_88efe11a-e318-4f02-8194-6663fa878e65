// P10-Web界面系统 Dashboard组件测试
// 测试预测仪表板的核心功能

import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import Dashboard from '../Dashboard'

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn()
  }
}))

// Mock hooks
vi.mock('../hooks/usePredictionData', () => ({
  usePredictionData: () => ({
    predictions: [
      {
        id: 1,
        hundreds_digit: 1,
        tens_digit: 2,
        units_digit: 3,
        combined_probability: 0.85,
        confidence_level: 'high',
        created_at: '2025-08-08T10:00:00Z'
      }
    ],
    statistics: {
      total_predictions: 100,
      avg_accuracy: 0.75,
      high_confidence_count: 25
    },
    loading: false,
    error: null,
    lastUpdate: new Date(),
    refreshData: vi.fn(),
    getConfidenceColor: vi.fn(() => '#52c41a'),
    getConfidenceText: vi.fn(() => '高'),
    formatProbability: vi.fn((p) => `${(p * 100).toFixed(1)}%`),
    formatPredictionNumber: vi.fn((h, t, u) => `${h}${t}${u}`),
    calculateFeatures: vi.fn()
  })
}))

vi.mock('../hooks/useRealTimeData', () => ({
  useRealTimeData: () => ({
    data: {
      predictions: [],
      systemStatus: { status: 'healthy' },
      performanceMetrics: {},
      optimizationTasks: [],
      lastUpdate: new Date()
    },
    isRealTime: true,
    wsStatus: { connected: true },
    refreshData: vi.fn(),
    reconnectWebSocket: vi.fn(),
    dataFreshness: 'fresh'
  })
}))

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders dashboard title', () => {
    render(<Dashboard />)
    expect(screen.getByText('🎯 福彩3D智能预测仪表板')).toBeInTheDocument()
  })

  test('displays prediction statistics', async () => {
    render(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText('总预测数')).toBeInTheDocument()
      expect(screen.getByText('平均准确率')).toBeInTheDocument()
      expect(screen.getByText('高置信度预测')).toBeInTheDocument()
    })
  })

  test('shows latest predictions table', async () => {
    render(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText('最新预测结果')).toBeInTheDocument()
      expect(screen.getByText('预测号码')).toBeInTheDocument()
      expect(screen.getByText('置信度')).toBeInTheDocument()
      expect(screen.getByText('预测时间')).toBeInTheDocument()
    })
  })

  test('displays system status cards', async () => {
    render(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText('系统状态')).toBeInTheDocument()
      expect(screen.getByText('数据库连接')).toBeInTheDocument()
      expect(screen.getByText('优化状态')).toBeInTheDocument()
    })
  })

  test('handles refresh button click', async () => {
    const mockRefresh = vi.fn()
    
    // Mock the hook to return our mock function
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [],
      statistics: {},
      loading: false,
      error: null,
      refreshData: mockRefresh,
      getConfidenceColor: vi.fn(),
      getConfidenceText: vi.fn(),
      formatProbability: vi.fn(),
      formatPredictionNumber: vi.fn(),
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    const refreshButton = screen.getByRole('button', { name: /刷新/i })
    fireEvent.click(refreshButton)
    
    expect(mockRefresh).toHaveBeenCalled()
  })

  test('shows loading state', () => {
    // Mock loading state
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [],
      statistics: {},
      loading: true,
      error: null,
      refreshData: vi.fn(),
      getConfidenceColor: vi.fn(),
      getConfidenceText: vi.fn(),
      formatProbability: vi.fn(),
      formatPredictionNumber: vi.fn(),
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    expect(screen.getByText('正在加载预测数据...')).toBeInTheDocument()
  })

  test('shows error state', () => {
    // Mock error state
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [],
      statistics: {},
      loading: false,
      error: '网络连接失败',
      refreshData: vi.fn(),
      getConfidenceColor: vi.fn(),
      getConfidenceText: vi.fn(),
      formatProbability: vi.fn(),
      formatPredictionNumber: vi.fn(),
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    expect(screen.getByText('预测数据加载失败')).toBeInTheDocument()
    expect(screen.getByText('网络连接失败')).toBeInTheDocument()
  })

  test('formats prediction numbers correctly', () => {
    const mockFormatPredictionNumber = vi.fn((h, t, u) => `${h}${t}${u}`)
    
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [
        {
          id: 1,
          hundreds_digit: 1,
          tens_digit: 2,
          units_digit: 3,
          combined_probability: 0.85,
          confidence_level: 'high',
          created_at: '2025-08-08T10:00:00Z'
        }
      ],
      statistics: {},
      loading: false,
      error: null,
      refreshData: vi.fn(),
      getConfidenceColor: vi.fn(),
      getConfidenceText: vi.fn(),
      formatProbability: vi.fn(),
      formatPredictionNumber: mockFormatPredictionNumber,
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    expect(mockFormatPredictionNumber).toHaveBeenCalledWith(1, 2, 3)
  })

  test('displays confidence levels with correct colors', () => {
    const mockGetConfidenceColor = vi.fn(() => '#52c41a')
    const mockGetConfidenceText = vi.fn(() => '高')
    
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [
        {
          id: 1,
          hundreds_digit: 1,
          tens_digit: 2,
          units_digit: 3,
          combined_probability: 0.85,
          confidence_level: 'high',
          created_at: '2025-08-08T10:00:00Z'
        }
      ],
      statistics: {},
      loading: false,
      error: null,
      refreshData: vi.fn(),
      getConfidenceColor: mockGetConfidenceColor,
      getConfidenceText: mockGetConfidenceText,
      formatProbability: vi.fn(),
      formatPredictionNumber: vi.fn(),
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    expect(mockGetConfidenceColor).toHaveBeenCalledWith('high')
    expect(mockGetConfidenceText).toHaveBeenCalledWith('high')
  })

  test('handles empty predictions gracefully', () => {
    vi.mocked(require('../hooks/usePredictionData').usePredictionData).mockReturnValue({
      predictions: [],
      statistics: {
        total_predictions: 0,
        avg_accuracy: 0,
        high_confidence_count: 0
      },
      loading: false,
      error: null,
      refreshData: vi.fn(),
      getConfidenceColor: vi.fn(),
      getConfidenceText: vi.fn(),
      formatProbability: vi.fn(),
      formatPredictionNumber: vi.fn(),
      calculateFeatures: vi.fn()
    })
    
    render(<Dashboard />)
    
    expect(screen.getByText('暂无预测数据')).toBeInTheDocument()
  })
})
