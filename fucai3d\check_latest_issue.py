#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查福彩3D项目中所有数据库的最新期号
"""

import sqlite3
import os
from datetime import datetime

def check_database_latest_issue(db_file):
    """检查单个数据库的最新期号"""
    db_path = os.path.join('data', db_file)
    if not os.path.exists(db_path):
        print(f'❌ {db_file} 不存在')
        return None
    
    print(f'\n🔍 检查 {db_file}')
    print(f'📁 文件大小: {os.path.getsize(db_path) / 1024:.2f} KB')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f'📊 表数量: {len(tables)}')
        
        latest_issues = []
        
        for table in tables:
            table_name = table[0]
            try:
                # 检查表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                # 检查记录数
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f'  📋 {table_name}: {count} 条记录')
                    
                    # 如果有期号字段，查找最新期号
                    if 'issue' in column_names:
                        cursor.execute(f'SELECT MIN(issue), MAX(issue) FROM {table_name} WHERE issue IS NOT NULL')
                        result = cursor.fetchone()
                        if result[0] and result[1]:
                            print(f'    📅 期号范围: {result[0]} - {result[1]}')
                            latest_issues.append((table_name, result[1]))
                            
                            # 检查最新几条记录
                            cursor.execute(f'SELECT issue FROM {table_name} WHERE issue IS NOT NULL ORDER BY issue DESC LIMIT 5')
                            recent_issues = cursor.fetchall()
                            recent_list = [issue[0] for issue in recent_issues]
                            print(f'    🔢 最新5期: {recent_list}')
                    
                    # 检查是否有时间戳字段
                    time_fields = ['created_at', 'timestamp', 'update_time', 'date']
                    for time_field in time_fields:
                        if time_field in column_names:
                            cursor.execute(f'SELECT MAX({time_field}) FROM {table_name}')
                            latest_time = cursor.fetchone()[0]
                            if latest_time:
                                print(f'    ⏰ 最新时间({time_field}): {latest_time}')
                            break
                else:
                    print(f'  📋 {table_name}: 空表')
                        
            except Exception as e:
                print(f'  ❌ {table_name}: 检查失败 - {e}')
        
        conn.close()
        
        # 总结最新期号
        if latest_issues:
            print(f'\n📈 {db_file} 最新期号总结:')
            for table_name, latest_issue in latest_issues:
                print(f'  🎯 {table_name}: {latest_issue}')
            
            # 找出最大期号
            max_issue = max(latest_issues, key=lambda x: x[1])
            print(f'  🏆 数据库最新期号: {max_issue[1]} (来自 {max_issue[0]} 表)')
            return max_issue[1]
        else:
            print(f'  ⚠️ {db_file} 中未找到期号数据')
            return None
        
    except Exception as e:
        print(f'❌ 打开数据库失败: {e}')
        return None

def main():
    """主函数"""
    print("🎯 福彩3D数据库期号检查")
    print("=" * 60)
    
    # 检查三个数据库文件
    db_files = ['fucai3d.db', 'lottery.db', 'alerts.db']
    
    all_latest_issues = {}
    
    for db_file in db_files:
        latest_issue = check_database_latest_issue(db_file)
        if latest_issue:
            all_latest_issues[db_file] = latest_issue
    
    print("\n" + "=" * 60)
    print("🎯 总体期号状态汇总")
    
    if all_latest_issues:
        print("\n📊 各数据库最新期号:")
        for db_file, latest_issue in all_latest_issues.items():
            status = "✅" if latest_issue == "2025209" else "⚠️"
            print(f"  {status} {db_file}: {latest_issue}")
        
        # 检查是否都更新到2025209
        target_issue = "2025209"
        updated_dbs = [db for db, issue in all_latest_issues.items() if issue == target_issue]
        outdated_dbs = [db for db, issue in all_latest_issues.items() if issue != target_issue]
        
        print(f"\n🎯 目标期号: {target_issue}")
        print(f"✅ 已更新数据库: {updated_dbs if updated_dbs else '无'}")
        print(f"⚠️ 需要更新数据库: {outdated_dbs if outdated_dbs else '无'}")
        
        if not outdated_dbs:
            print("\n🎉 所有数据库都已更新到最新期号 2025209！")
        else:
            print(f"\n⚠️ 还有 {len(outdated_dbs)} 个数据库需要更新")
            
    else:
        print("❌ 未找到任何期号数据")
    
    print("\n" + "=" * 60)
    print("检查完成")

if __name__ == '__main__':
    main()
