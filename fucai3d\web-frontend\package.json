{"name": "web-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run"}, "keywords": ["react", "typescript", "vite", "antd", "福彩3D"], "author": "P10-Web界面系统", "license": "ISC", "description": "福彩3D预测闭环系统现代化Web界面", "dependencies": {"@ant-design/icons": "^6.0.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "typescript": "^5.9.2", "vite": "^7.1.0", "zustand": "^5.0.7"}}