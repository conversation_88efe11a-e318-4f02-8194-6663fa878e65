# P1模块依赖包
# 数据采集与存储基础模块所需的Python包

# HTTP请求库
requests>=2.31.0

# HTML解析库
beautifulsoup4>=4.12.0

# 数据处理库
pandas>=2.0.0
polars>=0.20.0

# 数据库相关
# sqlite3  # Python内置，无需安装

# 测试框架
pytest>=7.4.0
pytest-cov>=4.1.0

# 日志和配置
python-dotenv>=1.0.0

# 类型检查
typing-extensions>=4.7.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# P2模块新增依赖包
# 高级特征工程系统所需的Python包

# 特征工程库
feature-engine>=1.6.0

# 特征重要性分析
shap>=0.42.0

# 机器学习库
scikit-learn>=1.3.0
xgboost>=1.7.0

# 数值计算库
numpy>=1.24.0

# P10-Web界面系统额外依赖
# FastAPI Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# 系统监控
psutil>=5.9.0

# 异步支持
aiofiles>=23.2.0

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# HTTP客户端
httpx>=0.25.0

# 其他工具库
click>=8.1.0
rich>=13.0.0
