import sqlite3

# 检查数据库中的真实数据
conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()

# 获取所有表名
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("数据库表:", [t[0] for t in tables])

# 检查final_predictions表
if ('final_predictions',) in tables:
    cursor.execute("SELECT COUNT(*) FROM final_predictions")
    count = cursor.fetchone()[0]
    print(f"final_predictions表记录数: {count}")
    
    if count > 0:
        cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 5")
        issues = cursor.fetchall()
        print(f"最新期号: {[i[0] for i in issues]}")
        
        cursor.execute("SELECT * FROM final_predictions LIMIT 3")
        samples = cursor.fetchall()
        print("样本数据:")
        for sample in samples:
            print(f"  {sample}")

# 检查fusion_predictions表是否存在
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fusion_predictions'")
result = cursor.fetchone()
if result:
    print("fusion_predictions表存在")
else:
    print("❌ fusion_predictions表不存在！")

conn.close()
