{"name": "rc-tree", "version": "5.13.1", "description": "tree ui component for react", "engines": {"node": ">=10.x"}, "keywords": ["react", "react-component", "react-tree", "tree"], "files": ["assets", "es", "lib"], "homepage": "http://github.com/react-component/tree", "author": "<EMAIL>", "repository": {"type": "git", "url": "**************:react-component/tree.git"}, "bugs": {"url": "http://github.com/react-component/tree/issues"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d dist", "compile": "father build && lessc assets/index.less assets/index.css", "prepare": "husky", "prepublishOnly": "npm run compile && np --yolo --no-publish", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "coverage": "rc-test --coverage", "gh-pages": "npm run docs:build && npm run docs:deploy", "now-build": "npm run docs:build"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.1.0", "@types/jest": "^29.5.10", "@types/node": "^22.7.3", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/warning": "^3.0.0", "@umijs/fabric": "^4.0.1", "dumi": "^2.1.0", "eslint": "^8.55.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-unicorn": "^56.0.1", "father": "^4.4.0", "gh-pages": "^6.1.1", "glob": "^11.0.0", "husky": "^9.1.6", "less": "^4.2.1", "lint-staged": "^15.2.10", "np": "^10.0.5", "prettier": "^3.3.3", "rc-dialog": "^9.0.0", "rc-test": "^7.0.15", "rc-tooltip": "^6.3.2", "rc-trigger": "^5.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.1"}}