# P9系统监控API路由
# 为P10-Web界面系统提供P9系统监控数据接口

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import os
import sys
import psutil
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

router = APIRouter(prefix="/api/monitoring", tags=["P9系统监控"])

# 数据库路径
DB_PATH = "data/fucai3d.db"

@router.get("/status", summary="获取P9系统状态")
async def get_system_status():
    """获取P9系统整体状态"""
    try:
        status_data = {
            "system_health": await _check_system_health(),
            "database_status": _check_database_status(),
            "optimization_status": await _get_optimization_status(),
            "performance_summary": await _get_performance_summary(),
            "active_components": _get_active_components(),
            "system_resources": _get_system_resources(),
            "last_update": datetime.now().isoformat()
        }
        
        return {
            "status": "success",
            "data": status_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@router.get("/metrics", summary="获取性能指标")
async def get_performance_metrics(hours: int = 24):
    """获取性能监控指标"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_performance_metrics(),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        try:
            # 获取性能监控数据
            query = """
                SELECT component_name, performance_metric, current_value,
                       threshold_value, status, monitor_time
                FROM enhanced_performance_monitor
                WHERE monitor_time >= ? AND monitor_time <= ?
                ORDER BY monitor_time DESC
            """
            
            metrics = pd.read_sql_query(
                query, conn,
                params=[start_time.isoformat(), end_time.isoformat()]
            )
            
            conn.close()
            
            if metrics.empty:
                return {
                    "status": "success",
                    "data": _get_mock_performance_metrics(),
                    "message": "使用模拟数据（无监控记录）"
                }
            
            # 按组件和指标分组
            grouped_metrics = {}
            for _, row in metrics.iterrows():
                component = row['component_name']
                metric = row['performance_metric']
                
                if component not in grouped_metrics:
                    grouped_metrics[component] = {}
                
                if metric not in grouped_metrics[component]:
                    grouped_metrics[component][metric] = []
                
                grouped_metrics[component][metric].append({
                    'value': row['current_value'],
                    'threshold': row['threshold_value'],
                    'status': row['status'],
                    'timestamp': row['monitor_time']
                })
            
            return {
                "status": "success",
                "data": grouped_metrics,
                "time_range": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat(),
                    "hours": hours
                }
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_performance_metrics(),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")

@router.get("/tasks", summary="获取优化任务状态")
async def get_optimization_tasks():
    """获取当前优化任务状态"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_optimization_tasks(),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        try:
            # 获取优化任务数据
            query = """
                SELECT task_id, task_type, task_status, start_time, end_time,
                       progress, result_summary, error_message
                FROM optimization_tasks
                WHERE start_time >= datetime('now', '-7 days')
                ORDER BY start_time DESC
                LIMIT 50
            """
            
            tasks = pd.read_sql_query(query, conn)
            conn.close()
            
            if tasks.empty:
                return {
                    "status": "success",
                    "data": _get_mock_optimization_tasks(),
                    "message": "使用模拟数据（无任务记录）"
                }
            
            return {
                "status": "success",
                "data": tasks.to_dict('records')
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_optimization_tasks(),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取优化任务失败: {str(e)}")

@router.get("/health", summary="P9系统健康检查")
async def health_check():
    """详细的P9系统健康检查"""
    try:
        health_data = {
            "overall_status": await _check_system_health(),
            "components": await _check_component_health(),
            "database": _check_database_health(),
            "resources": _check_resource_health(),
            "services": _check_service_health(),
            "check_time": datetime.now().isoformat()
        }
        
        return {
            "status": "success",
            "data": health_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@router.post("/trigger-optimization", summary="手动触发优化任务")
async def trigger_optimization(task_type: str, parameters: Optional[Dict] = None):
    """手动触发P9优化任务"""
    try:
        # 这里应该调用实际的P9优化管理器
        # 暂时返回模拟结果
        task_id = f"manual_{int(time.time())}"
        
        result = {
            "task_id": task_id,
            "task_type": task_type,
            "status": "started",
            "start_time": datetime.now().isoformat(),
            "estimated_duration": "5-10分钟",
            "parameters": parameters or {}
        }
        
        return {
            "status": "success",
            "data": result,
            "message": f"优化任务 {task_type} 已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发优化任务失败: {str(e)}")

# 辅助函数
async def _check_system_health() -> str:
    """检查系统整体健康状态"""
    try:
        # 检查数据库
        if not os.path.exists(DB_PATH):
            return "warning"
        
        # 检查系统资源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_percent = psutil.virtual_memory().percent
        
        if cpu_percent > 90 or memory_percent > 90:
            return "critical"
        elif cpu_percent > 70 or memory_percent > 70:
            return "warning"
        
        return "healthy"
    except Exception:
        return "critical"

def _check_database_status() -> str:
    """检查数据库状态"""
    try:
        if not os.path.exists(DB_PATH):
            return "disconnected"
        
        conn = sqlite3.connect(DB_PATH)
        conn.execute("SELECT 1")
        conn.close()
        return "connected"
    except Exception:
        return "error"

async def _get_optimization_status() -> Dict:
    """获取优化状态"""
    return {
        "is_running": False,  # 实际应该检查P9优化管理器状态
        "last_run": None,
        "next_scheduled": None,
        "active_tasks": 0
    }

async def _get_performance_summary() -> Dict:
    """获取性能摘要"""
    return {
        "avg_response_time": 150.5,
        "success_rate": 0.95,
        "error_rate": 0.05,
        "throughput": 100
    }

def _get_active_components() -> List[str]:
    """获取活跃组件列表"""
    return [
        "智能优化管理器",
        "性能监控器", 
        "WebSocket管理器",
        "预测API服务"
    ]

def _get_system_resources() -> Dict:
    """获取系统资源使用情况"""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "network_io": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv
            }
        }
    except Exception:
        return {
            "cpu_percent": 0,
            "memory_percent": 0,
            "disk_percent": 0,
            "network_io": {"bytes_sent": 0, "bytes_recv": 0}
        }

async def _check_component_health() -> Dict:
    """检查各组件健康状态"""
    return {
        "optimization_manager": "healthy",
        "performance_monitor": "healthy", 
        "websocket_manager": "healthy",
        "prediction_api": "healthy"
    }

def _check_database_health() -> Dict:
    """检查数据库健康状态"""
    try:
        if not os.path.exists(DB_PATH):
            return {"status": "missing", "size": 0, "tables": 0}
        
        conn = sqlite3.connect(DB_PATH)
        
        # 获取数据库大小
        size = os.path.getsize(DB_PATH)
        
        # 获取表数量
        tables = conn.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'").fetchone()[0]
        
        conn.close()
        
        return {
            "status": "healthy",
            "size": size,
            "tables": tables,
            "path": DB_PATH
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}

def _check_resource_health() -> Dict:
    """检查资源健康状态"""
    try:
        cpu = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory().percent
        
        return {
            "cpu": {"usage": cpu, "status": "healthy" if cpu < 80 else "warning"},
            "memory": {"usage": memory, "status": "healthy" if memory < 80 else "warning"}
        }
    except Exception:
        return {"cpu": {"usage": 0, "status": "unknown"}, "memory": {"usage": 0, "status": "unknown"}}

def _check_service_health() -> Dict:
    """检查服务健康状态"""
    return {
        "fastapi": "running",
        "websocket": "running",
        "background_tasks": "running"
    }

def _get_mock_performance_metrics() -> Dict:
    """获取模拟性能指标"""
    import random
    return {
        "prediction_accuracy": {
            "accuracy_rate": [
                {"value": round(random.uniform(0.6, 0.9), 3), "timestamp": datetime.now().isoformat()}
                for _ in range(10)
            ]
        },
        "system_performance": {
            "response_time": [
                {"value": round(random.uniform(100, 300), 1), "timestamp": datetime.now().isoformat()}
                for _ in range(10)
            ]
        }
    }

def _get_mock_optimization_tasks() -> List[Dict]:
    """获取模拟优化任务"""
    import random
    tasks = []
    for i in range(5):
        tasks.append({
            "task_id": f"task_{i+1}",
            "task_type": random.choice(["parameter_optimization", "model_training", "data_analysis"]),
            "task_status": random.choice(["running", "completed", "failed", "pending"]),
            "start_time": (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat(),
            "progress": random.randint(0, 100),
            "result_summary": "任务执行正常" if random.choice([True, False]) else None
        })
    return tasks
