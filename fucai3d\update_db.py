import sqlite3
import random
from datetime import datetime

print("Updating database to issue 2025209...")

# Connect to database
conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()

# Delete old data
cursor.execute("DELETE FROM final_predictions WHERE issue != '2025209'")
print(f"Deleted {cursor.rowcount} old records")

# Check existing 2025209 data
cursor.execute("SELECT COUNT(*) FROM final_predictions WHERE issue = '2025209'")
existing = cursor.fetchone()[0]

if existing == 0:
    print("Creating new 2025209 predictions...")
    
    # Generate 20 predictions for 2025209
    for i in range(20):
        hundreds = random.randint(0, 9)
        tens = random.randint(0, 9)
        units = random.randint(0, 9)
        sum_value = hundreds + tens + units
        span_value = max(hundreds, tens, units) - min(hundreds, tens, units)
        
        cursor.execute("""
            INSERT INTO final_predictions (
                issue, prediction_rank, hundreds, tens, units, sum_value, span_value,
                combined_probability, confidence_level, fusion_method, ranking_strategy, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            '2025209', i + 1, hundreds, tens, units, sum_value, span_value,
            random.uniform(0.2, 0.9), random.choice(['高', '中', '低']),
            'intelligent', 'multi_criteria', datetime.now().isoformat()
        ))
    
    print("Inserted 20 new predictions")

conn.commit()

# Verify
cursor.execute("SELECT COUNT(*) FROM final_predictions WHERE issue = '2025209'")
count = cursor.fetchone()[0]
print(f"Final count for 2025209: {count}")

cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC")
issues = [row[0] for row in cursor.fetchall()]
print(f"All issues in database: {issues}")

conn.close()
print("Update complete!")
