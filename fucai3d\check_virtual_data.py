#!/usr/bin/env python3
"""
检查项目是否使用虚拟数据
严厉禁止使用虚拟数据，一切都需要数据库中的真实历史数据为基础依据
"""

import sqlite3
import os
import sys

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    db_files = ['data/fucai3d.db', 'data/lottery.db', 'data/alerts.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f'✅ {db_file} 存在')
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 获取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f'  📊 表数量: {len(tables)}')
                
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                    print(f'  - {table_name}: {count} 条记录')
                
                conn.close()
            except Exception as e:
                print(f'  ❌ 查询失败: {e}')
        else:
            print(f'❌ {db_file} 不存在')
        print()

def check_fusion_predictions_table():
    """检查fusion_predictions表是否存在"""
    print("🔍 检查fusion_predictions表...")
    
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 检查fusion_predictions表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fusion_predictions'")
        result = cursor.fetchone()
        
        if result:
            print("✅ fusion_predictions表存在")
            cursor.execute("SELECT COUNT(*) FROM fusion_predictions")
            count = cursor.fetchone()[0]
            print(f"  📊 记录数量: {count}")
            
            if count > 0:
                cursor.execute("SELECT DISTINCT issue FROM fusion_predictions ORDER BY issue DESC LIMIT 5")
                issues = cursor.fetchall()
                print(f"  📅 最新期号: {[i[0] for i in issues]}")
        else:
            print("❌ fusion_predictions表不存在！")
            print("  🚨 这意味着API会fallback到模拟数据！")
        
        conn.close()
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def check_api_fallback_usage():
    """检查API是否正在使用模拟数据"""
    print("🔍 检查API是否使用模拟数据...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8000/api/prediction/latest?limit=1')
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', '')
            
            if '模拟数据' in message or 'mock' in message.lower():
                print("🚨 API正在使用模拟数据！")
                print(f"  消息: {message}")
                return True
            else:
                print("✅ API使用真实数据")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ API检查失败: {e}")
        return None

def main():
    print("🚨 虚拟数据合规性检查")
    print("=" * 50)
    print("项目要求：严厉禁止使用虚拟数据，一切都需要数据库中的真实历史数据为基础依据")
    print()
    
    # 检查数据库状态
    check_database_status()
    
    # 检查fusion_predictions表
    check_fusion_predictions_table()
    
    # 检查API是否使用模拟数据
    api_using_mock = check_api_fallback_usage()
    
    print("\n" + "=" * 50)
    print("📋 合规性评估结果:")
    
    if api_using_mock:
        print("❌ 严重违规：系统正在使用虚拟/模拟数据！")
        print("🔧 需要立即修复：")
        print("  1. 创建fusion_predictions表")
        print("  2. 填充真实历史数据")
        print("  3. 移除所有模拟数据fallback机制")
        sys.exit(1)
    elif api_using_mock is False:
        print("✅ 合规：系统使用真实数据")
        sys.exit(0)
    else:
        print("⚠️ 无法确定：API检查失败")
        sys.exit(2)

if __name__ == "__main__":
    main()
