{"version": 3, "file": "index.js", "sources": ["../throttle.js", "../debounce.js"], "sourcesContent": ["/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param {number} delay -                  A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher)\n *                                            are most useful.\n * @param {Function} callback -               A function to be executed after delay milliseconds. The `this` context and all arguments are passed through,\n *                                            as-is, to `callback` when the throttled-function is executed.\n * @param {object} [options] -              An object to configure options.\n * @param {boolean} [options.noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds\n *                                            while the throttled-function is being called. If noTrailing is false or unspecified, callback will be executed\n *                                            one final time after the last throttled-function call. (After the throttled-function has not been called for\n *                                            `delay` milliseconds, the internal counter is reset).\n * @param {boolean} [options.noLeading] -   Optional, defaults to false. If noLeading is false, the first throttled-function call will execute callback\n *                                            immediately. If noLeading is true, the first the callback execution will be skipped. It should be noted that\n *                                            callback will never executed if both noLeading = true and noTrailing = true.\n * @param {boolean} [options.debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is\n *                                            false (at end), schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function} A new, throttled, function.\n */\nexport default function (delay, callback, options) {\n\tconst {\n\t\tnoTrailing = false,\n\t\tnoLeading = false,\n\t\tdebounceMode = undefined\n\t} = options || {};\n\t/*\n\t * After wrapper has stopped being called, this timeout ensures that\n\t * `callback` is executed at the proper times in `throttle` and `end`\n\t * debounce modes.\n\t */\n\tlet timeoutID;\n\tlet cancelled = false;\n\n\t// Keep track of the last time `callback` was executed.\n\tlet lastExec = 0;\n\n\t// Function to clear existing timeout\n\tfunction clearExistingTimeout() {\n\t\tif (timeoutID) {\n\t\t\tclearTimeout(timeoutID);\n\t\t}\n\t}\n\n\t// Function to cancel next exec\n\tfunction cancel(options) {\n\t\tconst { upcomingOnly = false } = options || {};\n\t\tclearExistingTimeout();\n\t\tcancelled = !upcomingOnly;\n\t}\n\n\t/*\n\t * The `wrapper` function encapsulates all of the throttling / debouncing\n\t * functionality and when executed will limit the rate at which `callback`\n\t * is executed.\n\t */\n\tfunction wrapper(...arguments_) {\n\t\tlet self = this;\n\t\tlet elapsed = Date.now() - lastExec;\n\n\t\tif (cancelled) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Execute `callback` and update the `lastExec` timestamp.\n\t\tfunction exec() {\n\t\t\tlastExec = Date.now();\n\t\t\tcallback.apply(self, arguments_);\n\t\t}\n\n\t\t/*\n\t\t * If `debounceMode` is true (at begin) this is used to clear the flag\n\t\t * to allow future `callback` executions.\n\t\t */\n\t\tfunction clear() {\n\t\t\ttimeoutID = undefined;\n\t\t}\n\n\t\tif (!noLeading && debounceMode && !timeoutID) {\n\t\t\t/*\n\t\t\t * Since `wrapper` is being called for the first time and\n\t\t\t * `debounceMode` is true (at begin), execute `callback`\n\t\t\t * and noLeading != true.\n\t\t\t */\n\t\t\texec();\n\t\t}\n\n\t\tclearExistingTimeout();\n\n\t\tif (debounceMode === undefined && elapsed > delay) {\n\t\t\tif (noLeading) {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode with noLeading, if `delay` time has\n\t\t\t\t * been exceeded, update `lastExec` and schedule `callback`\n\t\t\t\t * to execute after `delay` ms.\n\t\t\t\t */\n\t\t\t\tlastExec = Date.now();\n\t\t\t\tif (!noTrailing) {\n\t\t\t\t\ttimeoutID = setTimeout(debounceMode ? clear : exec, delay);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode without noLeading, if `delay` time has been exceeded, execute\n\t\t\t\t * `callback`.\n\t\t\t\t */\n\t\t\t\texec();\n\t\t\t}\n\t\t} else if (noTrailing !== true) {\n\t\t\t/*\n\t\t\t * In trailing throttle mode, since `delay` time has not been\n\t\t\t * exceeded, schedule `callback` to execute `delay` ms after most\n\t\t\t * recent execution.\n\t\t\t *\n\t\t\t * If `debounceMode` is true (at begin), schedule `clear` to execute\n\t\t\t * after `delay` ms.\n\t\t\t *\n\t\t\t * If `debounceMode` is false (at end), schedule `callback` to\n\t\t\t * execute after `delay` ms.\n\t\t\t */\n\t\t\ttimeoutID = setTimeout(\n\t\t\t\tdebounceMode ? clear : exec,\n\t\t\t\tdebounceMode === undefined ? delay - elapsed : delay\n\t\t\t);\n\t\t}\n\t}\n\n\twrapper.cancel = cancel;\n\n\t// Return the wrapper function.\n\treturn wrapper;\n}\n", "/* eslint-disable no-undefined */\n\nimport throttle from './throttle.js';\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param {number} delay -               A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param {Function} callback -          A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                        to `callback` when the debounced-function is executed.\n * @param {object} [options] -           An object to configure options.\n * @param {boolean} [options.atBegin] -  Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                        after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                        (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n *\n * @returns {Function} A new, debounced function.\n */\nexport default function (delay, callback, options) {\n\tconst { atBegin = false } = options || {};\n\treturn throttle(delay, callback, { debounceMode: atBegin !== false });\n}\n"], "names": ["delay", "callback", "options", "_ref", "_ref$noTrailing", "noTrailing", "_ref$noLeading", "noLeading", "_ref$debounceMode", "debounceMode", "undefined", "timeoutID", "cancelled", "lastExec", "clearExistingTimeout", "clearTimeout", "cancel", "_ref2", "_ref2$upcomingOnly", "upcomingOnly", "wrapper", "_len", "arguments", "length", "arguments_", "Array", "_key", "self", "elapsed", "Date", "now", "exec", "apply", "clear", "setTimeout", "_ref$atBegin", "atBegin", "throttle"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,mBAAUA,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;AAClD,EAAA,IAAAC,IAAA,GAIID,OAAO,IAAI,EAAE;IAAAE,eAAA,GAAAD,IAAA,CAHhBE,UAAU;AAAVA,IAAAA,UAAU,GAAAD,eAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,eAAA;IAAAE,cAAA,GAAAH,IAAA,CAClBI,SAAS;AAATA,IAAAA,SAAS,GAAAD,cAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,cAAA;IAAAE,iBAAA,GAAAL,IAAA,CACjBM,YAAY;AAAZA,IAAAA,YAAY,GAAAD,iBAAA,KAAGE,KAAAA,CAAAA,GAAAA,SAAS,GAAAF,iBAAA,CAAA;AAEzB;AACD;AACA;AACA;AACA;AACC,EAAA,IAAIG,SAAS,CAAA;EACb,IAAIC,SAAS,GAAG,KAAK,CAAA;;AAErB;EACA,IAAIC,QAAQ,GAAG,CAAC,CAAA;;AAEhB;EACA,SAASC,oBAAoBA,GAAG;AAC/B,IAAA,IAAIH,SAAS,EAAE;MACdI,YAAY,CAACJ,SAAS,CAAC,CAAA;AACxB,KAAA;AACD,GAAA;;AAEA;EACA,SAASK,MAAMA,CAACd,OAAO,EAAE;AACxB,IAAA,IAAAe,KAAA,GAAiCf,OAAO,IAAI,EAAE;MAAAgB,kBAAA,GAAAD,KAAA,CAAtCE,YAAY;AAAZA,MAAAA,YAAY,GAAAD,kBAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,kBAAA,CAAA;AAC5BJ,IAAAA,oBAAoB,EAAE,CAAA;IACtBF,SAAS,GAAG,CAACO,YAAY,CAAA;AAC1B,GAAA;;AAEA;AACD;AACA;AACA;AACA;EACC,SAASC,OAAOA,GAAgB;AAAA,IAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;AAAVF,MAAAA,UAAU,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;AAAA,KAAA;IAC7B,IAAIC,IAAI,GAAG,IAAI,CAAA;IACf,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAGjB,QAAQ,CAAA;AAEnC,IAAA,IAAID,SAAS,EAAE;AACd,MAAA,OAAA;AACD,KAAA;;AAEA;IACA,SAASmB,IAAIA,GAAG;AACflB,MAAAA,QAAQ,GAAGgB,IAAI,CAACC,GAAG,EAAE,CAAA;AACrB7B,MAAAA,QAAQ,CAAC+B,KAAK,CAACL,IAAI,EAAEH,UAAU,CAAC,CAAA;AACjC,KAAA;;AAEA;AACF;AACA;AACA;IACE,SAASS,KAAKA,GAAG;AAChBtB,MAAAA,SAAS,GAAGD,SAAS,CAAA;AACtB,KAAA;AAEA,IAAA,IAAI,CAACH,SAAS,IAAIE,YAAY,IAAI,CAACE,SAAS,EAAE;AAC7C;AACH;AACA;AACA;AACA;AACGoB,MAAAA,IAAI,EAAE,CAAA;AACP,KAAA;AAEAjB,IAAAA,oBAAoB,EAAE,CAAA;AAEtB,IAAA,IAAIL,YAAY,KAAKC,SAAS,IAAIkB,OAAO,GAAG5B,KAAK,EAAE;AAClD,MAAA,IAAIO,SAAS,EAAE;AACd;AACJ;AACA;AACA;AACA;AACIM,QAAAA,QAAQ,GAAGgB,IAAI,CAACC,GAAG,EAAE,CAAA;QACrB,IAAI,CAACzB,UAAU,EAAE;UAChBM,SAAS,GAAGuB,UAAU,CAACzB,YAAY,GAAGwB,KAAK,GAAGF,IAAI,EAAE/B,KAAK,CAAC,CAAA;AAC3D,SAAA;AACD,OAAC,MAAM;AACN;AACJ;AACA;AACA;AACI+B,QAAAA,IAAI,EAAE,CAAA;AACP,OAAA;AACD,KAAC,MAAM,IAAI1B,UAAU,KAAK,IAAI,EAAE;AAC/B;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACGM,MAAAA,SAAS,GAAGuB,UAAU,CACrBzB,YAAY,GAAGwB,KAAK,GAAGF,IAAI,EAC3BtB,YAAY,KAAKC,SAAS,GAAGV,KAAK,GAAG4B,OAAO,GAAG5B,KAChD,CAAC,CAAA;AACF,KAAA;AACD,GAAA;EAEAoB,OAAO,CAACJ,MAAM,GAAGA,MAAM,CAAA;;AAEvB;AACA,EAAA,OAAOI,OAAO,CAAA;AACf;;ACrIA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,mBAAUpB,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;AAClD,EAAA,IAAAC,IAAA,GAA4BD,OAAO,IAAI,EAAE;IAAAiC,YAAA,GAAAhC,IAAA,CAAjCiC,OAAO;AAAPA,IAAAA,OAAO,GAAAD,YAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,YAAA,CAAA;AACvB,EAAA,OAAOE,QAAQ,CAACrC,KAAK,EAAEC,QAAQ,EAAE;IAAEQ,YAAY,EAAE2B,OAAO,KAAK,KAAA;AAAM,GAAC,CAAC,CAAA;AACtE;;;;"}