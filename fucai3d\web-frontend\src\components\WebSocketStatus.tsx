import React from 'react'
import { <PERSON>ge, Tooltip, Button, Space } from 'antd'
import { 
  WifiOutlined, 
  DisconnectOutlined, 
  LoadingOutlined, 
  ReloadOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons'
import { WebSocketStatus as WSStatus } from '../hooks/useWebSocket'

interface WebSocketStatusProps {
  status: WSStatus
  onReconnect?: () => void
  showDetails?: boolean
}

const WebSocketStatus: React.FC<WebSocketStatusProps> = ({ 
  status, 
  onReconnect, 
  showDetails = false 
}) => {
  const getStatusInfo = () => {
    if (status.connecting) {
      return {
        status: 'processing' as const,
        text: '连接中',
        icon: <LoadingOutlined />,
        color: '#1890ff'
      }
    }
    
    if (status.connected) {
      return {
        status: 'success' as const,
        text: '已连接',
        icon: <WifiOutlined />,
        color: '#52c41a'
      }
    }
    
    if (status.error) {
      return {
        status: 'error' as const,
        text: '连接错误',
        icon: <ExclamationCircleOutlined />,
        color: '#ff4d4f'
      }
    }
    
    return {
      status: 'default' as const,
      text: '未连接',
      icon: <DisconnectOutlined />,
      color: '#d9d9d9'
    }
  }

  const statusInfo = getStatusInfo()

  const getTooltipContent = () => {
    const content = []
    
    content.push(`状态: ${statusInfo.text}`)
    
    if (status.connected) {
      content.push(`连接次数: ${status.connectionCount}`)
      if (status.lastMessage) {
        content.push(`最后消息: ${new Date(status.lastMessage.timestamp).toLocaleTimeString()}`)
      }
    }
    
    if (status.error) {
      content.push(`错误: ${status.error}`)
    }
    
    return content.join('\n')
  }

  if (showDetails) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 8,
        padding: '4px 8px',
        background: status.connected ? '#f6ffed' : '#fff2f0',
        border: `1px solid ${status.connected ? '#b7eb8f' : '#ffccc7'}`,
        borderRadius: '4px'
      }}>
        <Badge 
          status={statusInfo.status} 
          text={
            <Space size={4}>
              {statusInfo.icon}
              <span style={{ color: statusInfo.color }}>
                WebSocket {statusInfo.text}
              </span>
            </Space>
          }
        />
        
        {!status.connected && !status.connecting && onReconnect && (
          <Button 
            size="small" 
            type="link" 
            icon={<ReloadOutlined />}
            onClick={onReconnect}
            style={{ padding: 0, height: 'auto' }}
          >
            重连
          </Button>
        )}
        
        {status.connected && status.lastMessage && (
          <span style={{ fontSize: '12px', color: '#666' }}>
            {new Date(status.lastMessage.timestamp).toLocaleTimeString()}
          </span>
        )}
      </div>
    )
  }

  return (
    <Tooltip title={getTooltipContent()}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
        <Badge 
          status={statusInfo.status}
          text={
            <Space size={4}>
              {statusInfo.icon}
              <span style={{ color: statusInfo.color, fontSize: '12px' }}>
                {statusInfo.text}
              </span>
            </Space>
          }
        />
        
        {!status.connected && !status.connecting && onReconnect && (
          <Button 
            size="small" 
            type="text" 
            icon={<ReloadOutlined />}
            onClick={onReconnect}
            style={{ 
              padding: '0 4px', 
              height: '20px',
              fontSize: '12px'
            }}
          />
        )}
      </div>
    </Tooltip>
  )
}

export default WebSocketStatus
