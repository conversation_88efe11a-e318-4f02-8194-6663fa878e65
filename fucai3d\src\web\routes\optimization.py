# P9优化控制API路由
# 为P10-Web界面系统提供P9优化系统控制接口

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import os
import sys
import json
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

router = APIRouter(prefix="/api/optimization", tags=["P9优化控制"])

# 数据库路径
DB_PATH = "data/fucai3d.db"

@router.post("/trigger", summary="手动触发优化任务")
async def trigger_optimization(
    background_tasks: BackgroundTasks,
    task_type: str = "full_optimization",
    parameters: Optional[Dict[str, Any]] = None
):
    """手动触发P9优化任务"""
    try:
        # 验证任务类型
        valid_types = [
            "full_optimization",
            "parameter_tuning", 
            "model_training",
            "data_analysis",
            "performance_evaluation"
        ]
        
        if task_type not in valid_types:
            raise HTTPException(
                status_code=400, 
                detail=f"无效的任务类型。支持的类型: {', '.join(valid_types)}"
            )
        
        # 生成任务ID
        task_id = f"manual_{task_type}_{int(datetime.now().timestamp())}"
        
        # 默认参数
        default_params = {
            "epochs": 100,
            "learning_rate": 0.001,
            "batch_size": 32,
            "validation_split": 0.2,
            "early_stopping": True,
            "save_model": True
        }
        
        # 合并用户参数
        final_params = {**default_params, **(parameters or {})}
        
        # 记录任务到数据库
        task_record = {
            "task_id": task_id,
            "task_type": task_type,
            "task_status": "pending",
            "start_time": datetime.now().isoformat(),
            "parameters": json.dumps(final_params),
            "progress": 0,
            "created_by": "web_interface"
        }
        
        # 添加后台任务
        background_tasks.add_task(_execute_optimization_task, task_id, task_type, final_params)
        
        return {
            "status": "success",
            "data": {
                "task_id": task_id,
                "task_type": task_type,
                "status": "pending",
                "parameters": final_params,
                "estimated_duration": _estimate_task_duration(task_type),
                "start_time": datetime.now().isoformat()
            },
            "message": f"优化任务 {task_type} 已启动"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动优化任务失败: {str(e)}")

@router.get("/config", summary="获取优化配置")
async def get_optimization_config():
    """获取当前优化配置"""
    try:
        # 默认配置
        config = {
            "model_settings": {
                "xgboost": {
                    "n_estimators": 100,
                    "max_depth": 6,
                    "learning_rate": 0.1,
                    "subsample": 0.8,
                    "colsample_bytree": 0.8
                },
                "lightgbm": {
                    "n_estimators": 100,
                    "max_depth": 6,
                    "learning_rate": 0.1,
                    "num_leaves": 31,
                    "feature_fraction": 0.8
                },
                "lstm": {
                    "units": 50,
                    "dropout": 0.2,
                    "recurrent_dropout": 0.2,
                    "epochs": 100,
                    "batch_size": 32
                }
            },
            "optimization_settings": {
                "auto_optimization": True,
                "optimization_interval": 24,  # 小时
                "performance_threshold": 0.8,
                "max_concurrent_tasks": 3,
                "enable_notifications": True
            },
            "data_settings": {
                "training_data_size": 1000,
                "validation_split": 0.2,
                "feature_selection": True,
                "data_preprocessing": True,
                "outlier_detection": True
            },
            "system_settings": {
                "log_level": "INFO",
                "max_log_files": 10,
                "backup_frequency": 7,  # 天
                "cleanup_old_models": True,
                "model_retention_days": 30
            }
        }
        
        return {
            "status": "success",
            "data": config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.put("/config", summary="更新优化配置")
async def update_optimization_config(config: Dict[str, Any]):
    """更新优化配置"""
    try:
        # 验证配置格式
        required_sections = ["model_settings", "optimization_settings", "data_settings", "system_settings"]
        
        for section in required_sections:
            if section not in config:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必需的配置节: {section}"
                )
        
        # 这里应该保存配置到文件或数据库
        # 暂时返回成功响应
        
        return {
            "status": "success",
            "data": config,
            "message": "配置已更新"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

@router.get("/diagnostics", summary="系统诊断")
async def run_system_diagnostics():
    """运行系统诊断"""
    try:
        diagnostics = {
            "system_info": _get_system_info(),
            "database_check": _check_database_health(),
            "model_status": _check_model_status(),
            "performance_metrics": _get_performance_diagnostics(),
            "resource_usage": _get_resource_diagnostics(),
            "error_logs": _get_recent_errors(),
            "recommendations": _generate_recommendations()
        }
        
        return {
            "status": "success",
            "data": diagnostics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"系统诊断失败: {str(e)}")

@router.get("/tasks/{task_id}", summary="获取任务状态")
async def get_task_status(task_id: str):
    """获取特定任务的状态"""
    try:
        # 这里应该从数据库查询任务状态
        # 暂时返回模拟数据
        
        task_status = {
            "task_id": task_id,
            "task_type": "full_optimization",
            "status": "running",
            "progress": 65,
            "start_time": (datetime.now() - timedelta(minutes=30)).isoformat(),
            "estimated_completion": (datetime.now() + timedelta(minutes=15)).isoformat(),
            "current_step": "模型训练中",
            "logs": [
                {"timestamp": datetime.now().isoformat(), "level": "INFO", "message": "开始数据预处理"},
                {"timestamp": datetime.now().isoformat(), "level": "INFO", "message": "特征工程完成"},
                {"timestamp": datetime.now().isoformat(), "level": "INFO", "message": "开始模型训练"}
            ]
        }
        
        return {
            "status": "success",
            "data": task_status
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.delete("/tasks/{task_id}", summary="取消任务")
async def cancel_task(task_id: str):
    """取消正在运行的任务"""
    try:
        # 这里应该实际取消任务
        # 暂时返回成功响应
        
        return {
            "status": "success",
            "message": f"任务 {task_id} 已取消"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

# 辅助函数
async def _execute_optimization_task(task_id: str, task_type: str, parameters: Dict[str, Any]):
    """执行优化任务（后台任务）"""
    try:
        # 这里应该调用实际的P9优化系统
        # 暂时模拟任务执行
        
        print(f"开始执行优化任务: {task_id}")
        
        # 模拟任务执行过程
        for progress in range(0, 101, 10):
            await asyncio.sleep(1)  # 模拟工作
            print(f"任务 {task_id} 进度: {progress}%")
        
        print(f"任务 {task_id} 完成")
        
    except Exception as e:
        print(f"任务 {task_id} 执行失败: {str(e)}")

def _estimate_task_duration(task_type: str) -> str:
    """估算任务持续时间"""
    durations = {
        "full_optimization": "30-60分钟",
        "parameter_tuning": "15-30分钟",
        "model_training": "20-40分钟",
        "data_analysis": "10-20分钟",
        "performance_evaluation": "5-15分钟"
    }
    return durations.get(task_type, "未知")

def _get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import platform
    import psutil
    
    return {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "disk_total": psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total
    }

def _check_database_health() -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        if not os.path.exists(DB_PATH):
            return {"status": "missing", "message": "数据库文件不存在"}
        
        conn = sqlite3.connect(DB_PATH)
        
        # 检查表结构
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        table_count = len(tables)
        
        # 检查数据量
        total_records = 0
        for table in tables:
            try:
                count = conn.execute(f"SELECT COUNT(*) FROM {table[0]}").fetchone()[0]
                total_records += count
            except:
                pass
        
        conn.close()
        
        return {
            "status": "healthy",
            "table_count": table_count,
            "total_records": total_records,
            "file_size": os.path.getsize(DB_PATH)
        }
        
    except Exception as e:
        return {"status": "error", "message": str(e)}

def _check_model_status() -> Dict[str, Any]:
    """检查模型状态"""
    return {
        "xgboost": {"status": "loaded", "version": "1.0", "last_trained": "2025-08-08"},
        "lightgbm": {"status": "loaded", "version": "1.0", "last_trained": "2025-08-08"},
        "lstm": {"status": "loaded", "version": "1.0", "last_trained": "2025-08-08"},
        "ensemble": {"status": "loaded", "version": "1.0", "last_trained": "2025-08-08"}
    }

def _get_performance_diagnostics() -> Dict[str, Any]:
    """获取性能诊断"""
    return {
        "prediction_accuracy": 0.85,
        "response_time": 150.5,
        "throughput": 100,
        "error_rate": 0.05,
        "memory_usage": 75.2,
        "cpu_usage": 45.8
    }

def _get_resource_diagnostics() -> Dict[str, Any]:
    """获取资源诊断"""
    try:
        import psutil
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "network_io": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv
            }
        }
    except Exception:
        return {"error": "无法获取资源信息"}

def _get_recent_errors() -> list:
    """获取最近的错误日志"""
    return [
        {
            "timestamp": datetime.now().isoformat(),
            "level": "ERROR",
            "component": "P8组件",
            "message": "数据库连接失败"
        },
        {
            "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
            "level": "WARNING",
            "component": "性能监控",
            "message": "CPU使用率过高"
        }
    ]

def _generate_recommendations() -> list:
    """生成系统建议"""
    return [
        {
            "type": "performance",
            "priority": "high",
            "title": "数据库优化",
            "description": "建议创建数据库索引以提高查询性能",
            "action": "运行数据库优化脚本"
        },
        {
            "type": "maintenance",
            "priority": "medium",
            "title": "日志清理",
            "description": "日志文件过多，建议清理旧日志",
            "action": "执行日志清理任务"
        },
        {
            "type": "security",
            "priority": "low",
            "title": "配置备份",
            "description": "建议定期备份系统配置",
            "action": "设置自动备份计划"
        }
    ]
