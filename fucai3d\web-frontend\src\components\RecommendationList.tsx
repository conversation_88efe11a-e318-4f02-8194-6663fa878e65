import React from 'react'
import { Card, List, Tag, Progress, Tooltip } from 'antd'
import { TrophyOutlined, StarOutlined, FireOutlined } from '@ant-design/icons'
import { PredictionData } from '../hooks/usePredictionData'

interface RecommendationListProps {
  predictions: PredictionData[]
}

const RecommendationList: React.FC<RecommendationListProps> = ({ predictions }) => {
  const getConfidenceColor = (level: string) => {
    switch (level) {
      case 'high': return '#52c41a'
      case 'medium': return '#faad14'
      case 'low': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getConfidenceText = (level: string) => {
    switch (level) {
      case 'high': return '高'
      case 'medium': return '中'
      case 'low': return '低'
      default: return '未知'
    }
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <TrophyOutlined style={{ color: '#faad14' }} />
    if (rank <= 3) return <StarOutlined style={{ color: '#1890ff' }} />
    if (rank <= 5) return <FireOutlined style={{ color: '#ff4d4f' }} />
    return null
  }

  const formatPredictionNumber = (prediction: PredictionData) => {
    return `${prediction.hundreds}${prediction.tens}${prediction.units}`
  }

  return (
    <Card 
      title="🏆 推荐预测" 
      size="small"
      extra={<Tag color="gold">TOP {predictions.length}</Tag>}
    >
      <List
        dataSource={predictions}
        renderItem={(item, index) => (
          <List.Item style={{ padding: '12px 0', borderBottom: index === predictions.length - 1 ? 'none' : '1px solid #f0f0f0' }}>
            <div style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  {getRankIcon(item.prediction_rank)}
                  <span style={{ 
                    fontFamily: 'monospace', 
                    fontSize: '18px', 
                    fontWeight: 'bold',
                    color: item.prediction_rank <= 3 ? '#1890ff' : '#000'
                  }}>
                    {formatPredictionNumber(item)}
                  </span>
                </div>
                <Tag color={getConfidenceColor(item.confidence_level)} size="small">
                  {getConfidenceText(item.confidence_level)}
                </Tag>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                <span style={{ fontSize: '12px', color: '#666' }}>
                  概率: {(item.combined_probability * 100).toFixed(1)}%
                </span>
                <span style={{ fontSize: '12px', color: '#666' }}>
                  排名: #{item.prediction_rank}
                </span>
              </div>

              <Progress 
                percent={item.combined_probability * 100} 
                size="small" 
                showInfo={false}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />

              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginTop: 8,
                fontSize: '11px',
                color: '#999'
              }}>
                <Tooltip title="和值">
                  <span>和: {item.sum_value}</span>
                </Tooltip>
                <Tooltip title="跨度">
                  <span>跨: {item.span}</span>
                </Tooltip>
                <Tooltip title="约束分数">
                  <span>约: {item.constraint_score.toFixed(2)}</span>
                </Tooltip>
              </div>
            </div>
          </List.Item>
        )}
      />
      
      {predictions.length === 0 && (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px 20px',
          color: '#999'
        }}>
          <div style={{ fontSize: '48px', marginBottom: 16 }}>🎯</div>
          <div>暂无推荐预测</div>
          <div style={{ fontSize: '12px', marginTop: 8 }}>
            请等待系统生成最新预测结果
          </div>
        </div>
      )}

      {predictions.length > 0 && (
        <div style={{ 
          marginTop: 16, 
          padding: '12px', 
          background: '#f6ffed', 
          border: '1px solid #b7eb8f',
          borderRadius: '6px',
          fontSize: '12px'
        }}>
          <div style={{ fontWeight: 'bold', color: '#389e0d', marginBottom: 4 }}>
            💡 推荐说明
          </div>
          <div style={{ color: '#52c41a' }}>
            • 排名越靠前，预测准确率越高<br/>
            • 高置信度预测建议优先考虑<br/>
            • 综合考虑概率、和值、跨度等因素
          </div>
        </div>
      )}
    </Card>
  )
}

export default RecommendationList
