# P10-Web界面系统部署指南

## 概述

P10-Web界面系统是福彩3D预测闭环系统的现代化Web界面，采用FastAPI + React + TypeScript技术栈，提供实时预测展示、系统监控、历史分析和优化控制功能。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │────│  后端 (FastAPI)  │────│  P9优化系统      │
│   端口: 80/3000  │    │   端口: 8000     │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌─────────┐            ┌─────────┐            ┌─────────┐
    │  Nginx  │            │  缓存    │            │ 数据库   │
    │         │            │ 系统    │            │ SQLite  │
    └─────────┘            └─────────┘            └─────────┘
```

## 环境要求

### 开发环境
- Node.js 18+
- Python 3.11+
- Git

### 生产环境
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ 磁盘空间

## 快速开始

### 1. 开发模式部署

#### 后端启动
```bash
# 进入项目目录
cd fucai3d

# 安装Python依赖
pip install -r requirements.txt

# 启动后端服务
cd src/web
python app.py
```

#### 前端启动
```bash
# 进入前端目录
cd web-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问地址：
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 2. Docker部署

#### 单容器部署
```bash
# 构建后端镜像
docker build -f Dockerfile.backend -t fucai3d-backend .

# 构建前端镜像
docker build -f Dockerfile.frontend -t fucai3d-frontend .

# 运行后端容器
docker run -d --name fucai3d-backend -p 8000:8000 fucai3d-backend

# 运行前端容器
docker run -d --name fucai3d-frontend -p 80:80 fucai3d-frontend
```

#### Docker Compose部署（推荐）
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 配置说明

### 环境变量

#### 后端配置
```bash
# 环境模式
ENV=production

# 数据库配置
DATABASE_URL=sqlite:///data/fucai3d.db

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=2000

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log
```

#### 前端配置
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:8000

# WebSocket URL
VITE_WS_URL=ws://localhost:8000/ws

# 环境模式
VITE_NODE_ENV=production
```

### 数据库配置

系统使用SQLite数据库，数据文件位于 `data/fucai3d.db`。

#### 数据库初始化
```bash
# 创建数据目录
mkdir -p data

# 数据库会在首次启动时自动创建
```

#### 数据备份
```bash
# 备份数据库
cp data/fucai3d.db data/fucai3d_backup_$(date +%Y%m%d_%H%M%S).db

# 恢复数据库
cp data/fucai3d_backup_YYYYMMDD_HHMMSS.db data/fucai3d.db
```

## 健康检查

### 健康检查端点

#### 后端健康检查
```bash
# 基础健康检查
curl http://localhost:8000/health

# 就绪检查
curl http://localhost:8000/ready

# 缓存状态
curl http://localhost:8000/api/cache/stats
```

#### 前端健康检查
```bash
# 前端可用性检查
curl http://localhost:80/

# Nginx状态检查
curl http://localhost:80/nginx_status
```

### 监控指标

系统提供以下监控指标：

1. **系统资源**
   - CPU使用率
   - 内存使用率
   - 磁盘使用率
   - 网络I/O

2. **应用性能**
   - API响应时间
   - 缓存命中率
   - WebSocket连接数
   - 错误率

3. **业务指标**
   - 预测准确率
   - 优化任务状态
   - 用户活跃度

## 性能优化

### 缓存策略

1. **API响应缓存**
   - 预测数据: 1分钟
   - 统计数据: 5分钟
   - 配置数据: 30分钟

2. **前端缓存**
   - 组件懒加载
   - 数据缓存Hook
   - 图表渲染优化

### 数据库优化

1. **索引优化**
```sql
-- 创建常用查询索引
CREATE INDEX idx_predictions_created_at ON fusion_predictions(created_at);
CREATE INDEX idx_performance_monitor_time ON enhanced_performance_monitor(monitor_time);
```

2. **查询优化**
   - 使用分页查询
   - 限制返回字段
   - 使用缓存装饰器

## 安全配置

### HTTPS配置

#### Nginx SSL配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    location / {
        proxy_pass http://frontend:80;
    }
    
    location /api/ {
        proxy_pass http://backend:8000;
    }
}
```

### 防火墙配置
```bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8000/tcp  # 仅开发环境

# 启用防火墙
ufw enable
```

## 故障排除

### 常见问题

1. **后端启动失败**
```bash
# 检查端口占用
netstat -tlnp | grep 8000

# 检查依赖安装
pip list | grep fastapi

# 查看详细错误
python src/web/app.py
```

2. **前端构建失败**
```bash
# 清理缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
```

3. **Docker部署问题**
```bash
# 查看容器日志
docker-compose logs backend
docker-compose logs frontend

# 检查容器状态
docker-compose ps

# 重启服务
docker-compose restart
```

### 日志分析

#### 后端日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看性能日志
grep "slow query" logs/app.log
```

#### 前端日志
```bash
# 查看Nginx访问日志
docker-compose exec frontend tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
docker-compose exec frontend tail -f /var/log/nginx/error.log
```

## 维护操作

### 定期维护

1. **数据库维护**
```bash
# 清理过期数据
sqlite3 data/fucai3d.db "DELETE FROM fusion_predictions WHERE created_at < datetime('now', '-30 days');"

# 数据库优化
sqlite3 data/fucai3d.db "VACUUM;"
```

2. **缓存维护**
```bash
# 清理缓存
curl -X POST http://localhost:8000/api/cache/clear

# 缓存预热
curl -X POST http://localhost:8000/api/cache/warmup
```

3. **日志轮转**
```bash
# 配置logrotate
echo "/app/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
}" > /etc/logrotate.d/fucai3d
```

### 更新部署

1. **滚动更新**
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 滚动更新
docker-compose up -d --no-deps backend
docker-compose up -d --no-deps frontend
```

2. **版本回滚**
```bash
# 查看镜像历史
docker images fucai3d-backend

# 回滚到指定版本
docker tag fucai3d-backend:previous fucai3d-backend:latest
docker-compose up -d
```

## 联系支持

如有问题，请联系开发团队或查看项目文档：
- 项目仓库: https://github.com/your-org/fucai3d
- 问题反馈: https://github.com/your-org/fucai3d/issues
- 技术文档: https://docs.your-domain.com/fucai3d
